# 测试架构改进使用指南

## 🎯 改进概述

基于RLS重复创建问题的分析，我们实施了以下关键改进：

1. **分离测试和生产初始化逻辑** - 避免重复初始化
2. **幂等的数据库操作** - 支持重复执行而不出错
3. **独立事务处理** - 避免单点失败影响全局
4. **调试友好的测试环境** - 支持数据保留和详细调试信息

## 🚀 快速开始

### 1. 使用新的调试功能

```bash
# 保留测试数据用于调试
pytest tests/integration/api/v1/admin/test_member_fixed_locks.py::TestMemberFixedSlotLockMultiTenantAPI::test_tenant_specific_lock_list --keep-test-data

# 启用RLS调试信息
pytest tests/integration/api/v1/admin/test_member_fixed_locks.py --debug-rls

# 组合使用
pytest tests/integration/api/v1/admin/test_member_fixed_locks.py --keep-test-data --debug-rls -v -s
```

### 2. 在测试中使用调试工具

```python
class TestYourAPI:
    def test_with_debug_info(self, client, admin_token, rls_debug_info, tenant_context_manager):
        """使用调试工具的测试示例"""
        
        # 检查RLS状态
        if rls_debug_info:
            info = rls_debug_info["get_rls_info"]("your_table_name")
            print(f"RLS Info: {info}")
        
        # 管理租户上下文
        with tenant_context_manager as ctx:
            ctx.set_tenant(1)
            
            # 在租户1的上下文中进行测试
            response = client.get("/api/v1/admin/your-endpoint/",
                                headers={"Authorization": f"Bearer {admin_token}"})
            assert response.status_code == 200
            
            # 上下文会自动重置
```

### 3. 调试RLS问题

```python
def test_rls_debugging(self, test_session, rls_debug_info):
    """RLS问题调试示例"""
    
    if not rls_debug_info:
        pytest.skip("需要 --debug-rls 选项")
    
    table_name = "member_fixed_slot_locks"
    
    # 1. 检查RLS状态
    rls_info = rls_debug_info["get_rls_info"](table_name)
    print(f"RLS enabled: {rls_info['rls_enabled']}")
    print(f"Policies: {len(rls_info['policies'])}")
    
    # 2. 查看所有数据（绕过RLS）
    all_data = rls_debug_info["query_all_data"](table_name)
    print(f"Total records in {table_name}: {len(all_data)}")
    
    # 3. 测试不同租户上下文下的数据可见性
    rls_debug_info["set_context"](1)
    tenant1_count = test_session.exec(text(f"SELECT COUNT(*) FROM {table_name}")).first()
    print(f"Tenant 1 can see: {tenant1_count} records")
    
    rls_debug_info["set_context"](2)
    tenant2_count = test_session.exec(text(f"SELECT COUNT(*) FROM {table_name}")).first()
    print(f"Tenant 2 can see: {tenant2_count} records")
    
    # 4. 重置上下文
    rls_debug_info["reset_context"]()
```

## 🔧 新功能详解

### 1. 幂等的数据库初始化

**问题**：之前的初始化逻辑不是幂等的，重复执行会失败
**解决**：新的 `tests/utils/db_setup.py` 模块提供幂等操作

```python
# 新的初始化逻辑
def setup_rls_policies_idempotent(engine):
    """幂等的RLS策略设置"""
    for table_name, tenant_column in RLS_TABLE_CONFIGS:
        with Session(engine) as session:
            try:
                # 检查策略是否已存在
                existing_policy = session.exec(text(f"""
                    SELECT 1 FROM pg_policies 
                    WHERE tablename = '{table_name}' AND policyname = '{policy_name}'
                """)).first()
                
                if not existing_policy:
                    # 只在策略不存在时创建
                    session.exec(text(policy_sql))
                
                session.commit()
            except Exception as e:
                session.rollback()
                continue  # 继续处理下一个表
```

### 2. 独立事务处理

**问题**：之前所有表的RLS设置在一个事务中，一个失败全部回滚
**解决**：每个表使用独立事务

```python
# 每个表独立处理
for table_name, tenant_column in RLS_TABLE_CONFIGS:
    with Session(engine) as session:  # 独立的session和事务
        try:
            # 处理这个表的RLS设置
            session.commit()
        except Exception as e:
            session.rollback()
            continue  # 不影响其他表
```

### 3. 调试友好的测试会话

**问题**：测试数据总是被回滚，无法调试
**解决**：支持可选的数据保留

```python
@pytest.fixture
def test_session(test_engine, request):
    """支持调试的测试会话"""
    connection = test_engine.connect()
    transaction = connection.begin()
    session = Session(bind=connection)
    
    yield session
    
    session.close()
    
    # 检查是否需要保留数据
    keep_data = request.config.getoption("--keep-test-data")
    if keep_data:
        transaction.commit()  # 保留数据
        print("💾 测试数据已保留用于调试")
    else:
        transaction.rollback()  # 正常回滚
    
    connection.close()
```

### 4. RLS调试工具

**问题**：RLS问题难以调试，缺乏可视化工具
**解决**：提供专门的调试fixture

```python
@pytest.fixture
def rls_debug_info(test_session, request):
    """RLS调试信息fixture"""
    debug_rls = request.config.getoption("--debug-rls")
    if not debug_rls:
        yield None
        return
    
    yield {
        "get_rls_info": lambda table: check_rls_status(test_session, table),
        "reset_context": lambda: reset_tenant_context(test_session),
        "set_context": lambda tid: set_tenant_context(test_session, tid),
        "query_all_data": lambda table: query_all_data(test_session, table)
    }
```

## 📋 迁移指南

### 从旧的测试架构迁移

**步骤1：更新测试命令**
```bash
# 旧的方式
pytest tests/integration/api/v1/admin/test_member_fixed_locks.py -v

# 新的方式（带调试）
pytest tests/integration/api/v1/admin/test_member_fixed_locks.py --keep-test-data --debug-rls -v -s
```

**步骤2：更新测试代码**
```python
# 旧的方式
def test_tenant_isolation(self, client, admin_token, second_tenant_admin_token):
    # 直接测试，没有调试信息
    pass

# 新的方式
def test_tenant_isolation(self, client, admin_token, second_tenant_admin_token, rls_debug_info):
    # 添加调试信息
    if rls_debug_info:
        info = rls_debug_info["get_rls_info"]("your_table")
        print(f"RLS Info: {info}")
    
    # 原有的测试逻辑
    pass
```

**步骤3：使用新的工具函数**
```python
# 在测试中使用工具函数
from tests.fixtures.database_improved import print_rls_debug_info, count_records_by_tenant

def test_with_debug_tools(self, test_session):
    # 打印RLS调试信息
    print_rls_debug_info(test_session, "member_fixed_slot_locks")
    
    # 统计各租户的记录数
    stats = count_records_by_tenant(test_session, "member_fixed_slot_locks")
    print(f"Records by tenant: {stats}")
```

## 🎯 最佳实践

### 1. 编写调试友好的测试

```python
class TestWithDebugging:
    """调试友好的测试示例"""
    
    def test_with_comprehensive_debug(self, client, admin_token, test_session, rls_debug_info, debug_mode):
        """包含全面调试信息的测试"""
        
        # 只在调试模式下输出详细信息
        if debug_mode:
            print(f"\n🔍 Starting test: {self.__class__.__name__}")
        
        # 执行测试逻辑
        response = client.get("/api/v1/admin/your-endpoint/",
                            headers={"Authorization": f"Bearer {admin_token}"})
        
        # 测试失败时提供调试信息
        if response.status_code != 200 and rls_debug_info:
            print(f"❌ Test failed, debugging info:")
            info = rls_debug_info["get_rls_info"]("your_table")
            print(f"  RLS Info: {info}")
            
            all_data = rls_debug_info["query_all_data"]("your_table")
            print(f"  Total records: {len(all_data)}")
        
        assert response.status_code == 200
```

### 2. 使用租户上下文管理器

```python
def test_multi_tenant_with_context_manager(self, tenant_context_manager, test_session):
    """使用上下文管理器的多租户测试"""
    
    with tenant_context_manager as ctx:
        # 测试租户1
        ctx.set_tenant(1)
        result1 = test_session.exec(text("SELECT COUNT(*) FROM your_table")).first()
        
        # 测试租户2
        ctx.set_tenant(2)
        result2 = test_session.exec(text("SELECT COUNT(*) FROM your_table")).first()
        
        # 上下文自动重置
    
    assert result1 != result2  # 验证租户隔离
```

### 3. 性能监控

```python
def test_with_performance_monitoring(self, client, admin_token, monitor_test_performance):
    """带性能监控的测试"""
    import time
    
    start_time = time.time()
    
    # 执行可能较慢的操作
    response = client.get("/api/v1/admin/large-dataset/",
                         headers={"Authorization": f"Bearer {admin_token}"})
    
    duration = time.time() - start_time
    
    # 性能断言
    assert duration < 5.0, f"Test took too long: {duration}s"
    assert response.status_code == 200
```

## 🔍 故障排查

### 常见问题及解决方案

**问题1：RLS策略仍然重复创建**
```bash
# 检查是否使用了新的初始化逻辑
grep -r "create_db_and_tables" tests/
# 应该只在 tests/utils/db_setup.py 中看到相关调用
```

**问题2：测试数据没有保留**
```bash
# 确保使用了正确的命令行选项
pytest your_test.py --keep-test-data

# 检查fixture是否正确处理了选项
# 在测试中添加调试输出
def test_debug(self, request):
    keep_data = request.config.getoption("--keep-test-data")
    print(f"Keep data option: {keep_data}")
```

**问题3：RLS调试信息不显示**
```bash
# 确保使用了调试选项
pytest your_test.py --debug-rls -s

# 检查fixture是否正确注入
def test_debug(self, rls_debug_info):
    if rls_debug_info is None:
        print("RLS debug info not available, use --debug-rls option")
```

## 📈 后续改进计划

1. **容器化测试环境** - 使用Docker提供一致的测试环境
2. **并行测试支持** - 为每个测试进程分配独立数据库
3. **测试数据快照** - 支持测试数据的快照和恢复
4. **可视化调试工具** - 提供Web界面查看测试数据和RLS状态
5. **自动化性能监控** - 集成到CI/CD流程中的性能回归检测
