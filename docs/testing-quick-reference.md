# 测试架构快速参考指南

## 🚨 当前已知问题

### RLS重复创建问题 ✅ 已修复
**问题**：测试时数据库初始化被调用两次，导致RLS策略创建失败
**解决**：为每个表使用独立事务，避免单点失败影响全局

### 测试数据无法保留问题 ⚠️ 待解决
**问题**：每次测试结束后数据被自动回滚，无法调试
**临时方案**：见下方"调试测试数据"部分

## 🔧 常用调试技巧

### 1. 保留测试数据进行调试

**方法1：修改fixture临时不回滚**
```python
# 在 tests/fixtures/database.py 中临时注释回滚
@pytest.fixture
def test_session(test_engine):
    connection = test_engine.connect()
    transaction = connection.begin()
    session = Session(bind=connection)
    yield session
    session.close()
    # transaction.rollback()  # 临时注释这行
    # connection.close()     # 临时注释这行
```

**方法2：使用专门的调试fixture**
```python
# 创建新的调试fixture
@pytest.fixture
def debug_session(test_engine):
    """调试用的数据库会话，不会回滚数据"""
    with Session(test_engine) as session:
        yield session
        # 不回滚，数据会保留
```

### 2. 检查RLS状态

```python
def check_rls_status(session, table_name):
    """检查表的RLS状态"""
    # 检查RLS是否启用
    rls_enabled = session.exec(text(f"""
        SELECT relrowsecurity FROM pg_class WHERE relname = '{table_name}'
    """)).first()
    
    # 检查RLS策略
    policies = session.exec(text(f"""
        SELECT policyname, qual FROM pg_policies WHERE tablename = '{table_name}'
    """)).all()
    
    print(f"Table {table_name}:")
    print(f"  RLS enabled: {rls_enabled}")
    print(f"  Policies: {policies}")
```

### 3. 检查租户上下文

```python
def check_tenant_context(session):
    """检查当前租户上下文"""
    current_tenant = session.exec(text(
        "SELECT current_setting('app.current_tenant_id', true)"
    )).first()
    print(f"Current tenant context: {current_tenant}")
```

### 4. 直接查询数据库（绕过RLS）

```python
def query_without_rls(session, table_name):
    """绕过RLS查询数据"""
    # 重置租户上下文
    session.exec(text("RESET app.current_tenant_id"))
    
    # 查询所有数据
    result = session.exec(text(f"SELECT * FROM {table_name}")).all()
    print(f"All data in {table_name}: {result}")
```

## 📋 测试编写最佳实践

### 1. 多租户测试模板

```python
class TestMultiTenantAPI:
    """多租户API测试模板"""
    
    def test_tenant_isolation(self, client, admin_token, second_tenant_admin_token, test_session):
        """测试租户隔离"""
        # 1. 创建第一个租户的数据
        response1 = client.post("/api/v1/admin/resource/", 
                               json={"name": "tenant1_data"},
                               headers={"Authorization": f"Bearer {admin_token}"})
        assert response1.status_code == 201
        
        # 2. 第二个租户不应该看到第一个租户的数据
        response2 = client.get("/api/v1/admin/resource/",
                              headers={"Authorization": f"Bearer {second_tenant_admin_token}"})
        assert response2.status_code == 200
        assert response2.json()["total"] == 0
        
        # 3. 调试信息（可选）
        if hasattr(self, 'debug'):
            check_rls_status(test_session, "your_table_name")
            check_tenant_context(test_session)
```

### 2. 数据创建最佳实践

```python
@pytest.fixture
def created_resource(test_session, created_tenant):
    """创建测试资源"""
    # 设置租户上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_tenant['id']}'"))
    
    # 创建资源
    resource = YourModel(
        name="test_resource",
        tenant_id=created_tenant["id"]
    )
    test_session.add(resource)
    test_session.commit()
    test_session.refresh(resource)
    
    return {
        "id": resource.id,
        "name": resource.name,
        "tenant_id": resource.tenant_id
    }
```

### 3. 错误测试模板

```python
def test_error_handling(self, client, admin_token):
    """测试错误处理"""
    response = client.post("/api/v1/admin/resource/",
                          json={"invalid": "data"},
                          headers={"Authorization": f"Bearer {admin_token}"})
    
    assert response.status_code == 400
    data = response.json()
    assert data["success"] is False
    assert "business_code" in data
    assert "message" in data
```

## 🐛 常见问题排查

### 问题1：测试中看不到预期的数据

**可能原因**：
- RLS策略阻止了数据访问
- 租户上下文设置错误
- 数据创建失败但没有抛出异常

**排查步骤**：
1. 检查RLS状态：`check_rls_status(session, table_name)`
2. 检查租户上下文：`check_tenant_context(session)`
3. 绕过RLS查询：`query_without_rls(session, table_name)`

### 问题2：RLS策略创建失败

**可能原因**：
- 策略已存在
- 表不存在
- 权限不足

**排查步骤**：
1. 检查表是否存在
2. 检查现有策略
3. 查看详细错误日志

### 问题3：测试间数据污染

**可能原因**：
- fixture作用域设置错误
- 事务回滚失败
- 全局状态未清理

**解决方案**：
1. 确保使用正确的fixture作用域
2. 检查事务回滚逻辑
3. 在测试开始时清理状态

## 🚀 快速开始新测试

### 1. 基础API测试

```python
class TestYourAPI:
    def test_create_success(self, client, admin_token, created_tenant):
        """测试创建成功"""
        data = {"name": "test", "description": "test desc"}
        response = client.post("/api/v1/admin/your-endpoint/",
                              json=data,
                              headers={"Authorization": f"Bearer {admin_token}"})
        assert response.status_code == 201
        
    def test_list_success(self, client, admin_token):
        """测试列表查询成功"""
        response = client.get("/api/v1/admin/your-endpoint/",
                             headers={"Authorization": f"Bearer {admin_token}"})
        assert response.status_code == 200
```

### 2. 多租户测试

```python
class TestYourMultiTenantAPI:
    def test_tenant_isolation(self, client, admin_token, second_tenant_admin_token):
        """测试租户隔离"""
        # 第一个租户创建数据
        response1 = client.post("/api/v1/admin/your-endpoint/",
                               json={"name": "tenant1_data"},
                               headers={"Authorization": f"Bearer {admin_token}"})
        assert response1.status_code == 201
        
        # 第二个租户查询，应该看不到第一个租户的数据
        response2 = client.get("/api/v1/admin/your-endpoint/",
                              headers={"Authorization": f"Bearer {second_tenant_admin_token}"})
        assert response2.status_code == 200
        assert response2.json()["total"] == 0
```

## 📚 相关文档

- [完整测试架构分析](./testing-architecture-analysis.md)
- [数据库设计文档](./database-design.md)
- [API设计规范](./api-design-guide.md)

## 🔗 有用的命令

```bash
# 运行特定测试并保留数据（需要实现）
pytest tests/integration/api/v1/admin/test_your_api.py::TestClass::test_method --keep-test-data

# 运行测试并显示详细输出
pytest tests/integration/api/v1/admin/test_your_api.py -v -s

# 运行多租户相关的所有测试
pytest -k "tenant" -v

# 运行集成测试
pytest tests/integration/ -v
```
