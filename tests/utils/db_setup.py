"""
测试数据库设置工具模块
专门用于测试环境的数据库初始化，与生产环境分离
"""
import logging
from sqlmodel import Session, SQLModel, text
from typing import List, Tuple

logger = logging.getLogger(__name__)

# RLS表配置
RLS_TABLE_CONFIGS = [
    ('users', 'tenant_id'),           # 用户表（super_admin的tenant_id为null，需要特殊处理）
    ('user_sessions', None),          # 用户会话表（通过users表关联）
    ('members', 'tenant_id'),         # 会员表
    ('tag_categories', 'tenant_id'),  # 标签分类表
    ('tags', 'tenant_id'),            # 标签表
    ('teachers', 'tenant_id'),        # 教师表
    ('teacher_tags', None),           # 教师标签关联表（通过教师表关联）
    ('teacher_fixed_slots', 'tenant_id'),  # 教师固定时间占位表
    ('course_system_configs', 'tenant_id'),  # 课程系统配置表
    ('scheduled_classes', 'tenant_id'),  # 已排课表
    ('member_fixed_slot_locks', 'tenant_id'),  # 会员固定课位锁定表
    ('member_card_templates', 'tenant_id'),  # 会员卡模板表
    ('member_cards', 'tenant_id'),    # 会员卡表
    ('recharge_records', 'tenant_id'),  # 充值记录表
    ('consumption_records', 'tenant_id'),  # 消费记录表
]


def setup_test_database(engine):
    """
    专门用于测试的数据库设置
    与生产环境的初始化逻辑分离，避免重复初始化问题
    """
    logger.info("Setting up test database...")
    
    # 1. 创建表结构
    create_tables(engine)
    
    # 2. 设置RLS策略（幂等）
    setup_rls_policies_idempotent(engine)
    
    # 3. 初始化基础测试数据（如果需要）
    init_basic_test_data(engine)
    
    logger.info("✅ Test database setup completed")


def create_tables(engine):
    """创建表结构"""
    try:
        # 导入所有模型以确保表被创建
        import app.models
        SQLModel.metadata.create_all(engine)
        logger.info("Database tables created")
    except Exception as e:
        logger.error(f"Failed to create tables: {e}")
        raise


def setup_rls_policies_idempotent(engine):
    """
    幂等的RLS策略设置
    每个表使用独立事务，避免单点失败影响全局
    """
    logger.info("Setting up RLS policies...")
    
    for table_name, tenant_column in RLS_TABLE_CONFIGS:
        with Session(engine) as session:
            try:
                # 检查表是否存在
                table_exists = session.exec(text(f"""
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_name = '{table_name}'
                """)).first()
                
                if not table_exists:
                    logger.warning(f"Table {table_name} does not exist, skipping RLS setup")
                    continue
                
                # 启用RLS
                enable_rls_for_table(session, table_name)
                
                # 创建策略
                if tenant_column:
                    create_tenant_isolation_policy(session, table_name, tenant_column)
                else:
                    create_association_table_policy(session, table_name)
                
                session.commit()
                logger.info(f"✅ RLS setup completed for table: {table_name}")
                
            except Exception as e:
                logger.warning(f"RLS setup failed for table {table_name}: {e}")
                session.rollback()
                # 继续处理下一个表，不要因为一个表的失败而中断整个过程
                continue


def enable_rls_for_table(session: Session, table_name: str):
    """为表启用RLS"""
    # 检查RLS是否已启用
    rls_enabled = session.exec(text(f"""
        SELECT relrowsecurity FROM pg_class WHERE relname = '{table_name}'
    """)).first()
    
    if not rls_enabled:
        session.exec(text(f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY"))
        session.exec(text(f"ALTER TABLE {table_name} FORCE ROW LEVEL SECURITY"))
        logger.info(f"Enabled RLS for table {table_name}")
    else:
        logger.info(f"RLS already enabled for table {table_name}")


def create_tenant_isolation_policy(session: Session, table_name: str, tenant_column: str):
    """创建租户隔离策略"""
    policy_name = f"{table_name}_tenant_isolation"
    
    # 检查策略是否已存在
    existing_policy = session.exec(text(f"""
        SELECT 1 FROM pg_policies 
        WHERE tablename = '{table_name}' AND policyname = '{policy_name}'
    """)).first()
    
    if existing_policy:
        logger.info(f"Policy {policy_name} already exists")
        return
    
    if table_name == 'users':
        # 用户表特殊处理：super_admin可以看到所有数据
        policy_sql = f"""
            CREATE POLICY {policy_name} ON {table_name}
            FOR ALL
            TO PUBLIC
            USING (
                {tenant_column} IS NULL OR
                {tenant_column} = COALESCE(
                    CASE
                        WHEN current_setting('app.current_tenant_id', true) = '' THEN NULL
                        ELSE current_setting('app.current_tenant_id', true)::int
                    END,
                    {tenant_column}
                )
            )
            WITH CHECK (
                {tenant_column} IS NULL OR
                {tenant_column} = CASE
                    WHEN current_setting('app.current_tenant_id', true) = '' THEN NULL
                    ELSE current_setting('app.current_tenant_id', true)::int
                END
            )
        """
    else:
        # 普通表：只能访问当前租户的数据
        policy_sql = f"""
            CREATE POLICY {policy_name} ON {table_name}
            FOR ALL
            TO PUBLIC
            USING (
                {tenant_column} = CASE
                    WHEN current_setting('app.current_tenant_id', true) = '' THEN NULL
                    ELSE current_setting('app.current_tenant_id', true)::int
                END
            )
            WITH CHECK (
                {tenant_column} = CASE
                    WHEN current_setting('app.current_tenant_id', true) = '' THEN NULL
                    ELSE current_setting('app.current_tenant_id', true)::int
                END
            )
        """
    
    session.exec(text(policy_sql))
    logger.info(f"Created policy {policy_name}")


def create_association_table_policy(session: Session, table_name: str):
    """为关联表创建策略"""
    policy_name = f"{table_name}_tenant_isolation"
    
    # 检查策略是否已存在
    existing_policy = session.exec(text(f"""
        SELECT 1 FROM pg_policies 
        WHERE tablename = '{table_name}' AND policyname = '{policy_name}'
    """)).first()
    
    if existing_policy:
        logger.info(f"Policy {policy_name} already exists")
        return
    
    if table_name == 'user_sessions':
        policy_sql = f"""
            CREATE POLICY {policy_name} ON {table_name}
            FOR ALL
            TO PUBLIC
            USING (
                user_id IN (
                    SELECT id FROM users
                    WHERE tenant_id IS NULL OR
                          tenant_id = COALESCE(
                              CASE
                                  WHEN current_setting('app.current_tenant_id', true) = '' THEN NULL
                                  ELSE current_setting('app.current_tenant_id', true)::int
                              END,
                              tenant_id
                          )
                )
            )
            WITH CHECK (
                user_id IN (
                    SELECT id FROM users
                    WHERE tenant_id IS NULL OR
                          tenant_id = CASE
                              WHEN current_setting('app.current_tenant_id', true) = '' THEN NULL
                              ELSE current_setting('app.current_tenant_id', true)::int
                          END
                )
            )
        """
    elif table_name == 'teacher_tags':
        policy_sql = f"""
            CREATE POLICY {policy_name} ON {table_name}
            FOR ALL
            TO PUBLIC
            USING (
                teacher_id IN (
                    SELECT id FROM teachers
                    WHERE tenant_id = CASE
                        WHEN current_setting('app.current_tenant_id', true) = '' THEN NULL
                        ELSE current_setting('app.current_tenant_id', true)::int
                    END
                )
            )
            WITH CHECK (
                teacher_id IN (
                    SELECT id FROM teachers
                    WHERE tenant_id = CASE
                        WHEN current_setting('app.current_tenant_id', true) = '' THEN NULL
                        ELSE current_setting('app.current_tenant_id', true)::int
                    END
                )
            )
        """
    else:
        logger.warning(f"No specific policy defined for association table {table_name}")
        return
    
    session.exec(text(policy_sql))
    logger.info(f"Created policy {policy_name}")


def init_basic_test_data(engine):
    """初始化基础测试数据（如果需要）"""
    # 这里可以添加一些基础的测试数据
    # 比如默认的租户计划模板、系统配置等
    logger.info("Basic test data initialization completed")


def check_rls_status(session: Session, table_name: str) -> dict:
    """检查表的RLS状态（调试用）"""
    # 检查RLS是否启用
    rls_enabled = session.exec(text(f"""
        SELECT relrowsecurity FROM pg_class WHERE relname = '{table_name}'
    """)).first()
    
    # 检查RLS策略
    policies = session.exec(text(f"""
        SELECT policyname, qual FROM pg_policies WHERE tablename = '{table_name}'
    """)).all()
    
    return {
        "table_name": table_name,
        "rls_enabled": bool(rls_enabled),
        "policies": [{"name": p[0], "condition": p[1]} for p in policies]
    }


def reset_tenant_context(session: Session):
    """重置租户上下文（调试用）"""
    try:
        session.exec(text("RESET app.current_tenant_id"))
        logger.info("Tenant context reset")
    except Exception as e:
        logger.warning(f"Failed to reset tenant context: {e}")


def set_tenant_context(session: Session, tenant_id: int):
    """设置租户上下文（调试用）"""
    try:
        session.exec(text(f"SET app.current_tenant_id = '{tenant_id}'"))
        logger.info(f"Tenant context set to {tenant_id}")
    except Exception as e:
        logger.warning(f"Failed to set tenant context: {e}")
