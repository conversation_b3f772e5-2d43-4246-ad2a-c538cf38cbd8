"""
改进的数据库测试fixtures
解决重复初始化和调试困难的问题
"""
import pytest
import os
from typing import Generator
from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy import text

# 测试数据库配置
TEST_POSTGRES_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
TEST_DATABASE_URL = TEST_POSTGRES_URL


def pytest_addoption(parser):
    """添加pytest命令行选项"""
    parser.addoption(
        "--keep-test-data",
        action="store_true",
        default=False,
        help="保留测试数据用于调试（不回滚事务）"
    )
    parser.addoption(
        "--test-db-name",
        action="store",
        default="course_booking_test_db",
        help="指定测试数据库名称"
    )
    parser.addoption(
        "--debug-rls",
        action="store_true",
        default=False,
        help="启用RLS调试信息"
    )


@pytest.fixture(scope="session")
def test_engine():
    """创建测试数据库引擎（改进版）"""
    connect_args = {}
    default_db_url = TEST_POSTGRES_URL.rsplit('/', 1)[0] + '/postgres'
    temp_engine = create_engine(default_db_url)
    with temp_engine.connect() as conn:
        conn.execute(text("COMMIT"))
        # 先删除已存在的测试数据库
        conn.execute(text("DROP DATABASE IF EXISTS course_booking_test_db"))
        # 创建新的测试数据库
        conn.execute(text("CREATE DATABASE course_booking_test_db"))
    temp_engine.dispose()

    engine = create_engine(
        TEST_DATABASE_URL,
        echo=False,
        connect_args=connect_args
    )
    
    # 使用专门的测试初始化函数，避免与生产环境耦合
    from tests.utils.db_setup import setup_test_database
    setup_test_database(engine)
    
    yield engine
    
    # 清理测试数据库
    SQLModel.metadata.drop_all(engine)
    engine.dispose()
    default_db_url = TEST_POSTGRES_URL.rsplit('/', 1)[0] + '/postgres'
    temp_engine = create_engine(default_db_url)
    with temp_engine.connect() as conn:
        conn.execute(text("COMMIT"))
        conn.execute(text("DROP DATABASE IF EXISTS course_booking_test_db"))
    temp_engine.dispose()


@pytest.fixture
def test_session(test_engine, request) -> Generator[Session, None, None]:
    """创建测试数据库会话（改进版，支持调试）"""
    connection = test_engine.connect()
    transaction = connection.begin()
    
    session = Session(bind=connection)
    session.execute(text("SET search_path TO public"))
    
    yield session
    
    session.close()
    
    # 检查是否需要保留数据用于调试
    keep_data = request.config.getoption("--keep-test-data")
    if keep_data:
        transaction.commit()
        print(f"\n💾 测试数据已保留在数据库中，可用于调试")
    else:
        transaction.rollback()
    
    connection.close()


@pytest.fixture
def debug_session(test_engine) -> Generator[Session, None, None]:
    """专门用于调试的数据库会话（不回滚数据）"""
    with Session(test_engine) as session:
        session.execute(text("SET search_path TO public"))
        yield session
        # 不回滚，数据会保留


@pytest.fixture
def rls_debug_info(test_session, request):
    """RLS调试信息fixture"""
    debug_rls = request.config.getoption("--debug-rls")
    if not debug_rls:
        yield None
        return
    
    from tests.utils.db_setup import check_rls_status, reset_tenant_context
    
    def get_rls_info(table_name: str):
        """获取指定表的RLS信息"""
        return check_rls_status(test_session, table_name)
    
    def reset_context():
        """重置租户上下文"""
        reset_tenant_context(test_session)
    
    def set_context(tenant_id: int):
        """设置租户上下文"""
        from tests.utils.db_setup import set_tenant_context
        set_tenant_context(test_session, tenant_id)
    
    def query_all_data(table_name: str):
        """查询表中的所有数据（绕过RLS）"""
        reset_context()
        result = test_session.exec(text(f"SELECT * FROM {table_name}")).all()
        return result
    
    yield {
        "get_rls_info": get_rls_info,
        "reset_context": reset_context,
        "set_context": set_context,
        "query_all_data": query_all_data
    }


@pytest.fixture
def tenant_context_manager(test_session):
    """租户上下文管理器"""
    from tests.utils.db_setup import set_tenant_context, reset_tenant_context
    
    class TenantContextManager:
        def __init__(self, session):
            self.session = session
            self.original_context = None
        
        def set_tenant(self, tenant_id: int):
            """设置租户上下文"""
            set_tenant_context(self.session, tenant_id)
        
        def reset(self):
            """重置租户上下文"""
            reset_tenant_context(self.session)
        
        def __enter__(self):
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            self.reset()
    
    return TenantContextManager(test_session)


# 向后兼容的fixture别名
@pytest.fixture
def test_session_legacy(test_engine) -> Generator[Session, None, None]:
    """向后兼容的测试会话（保持原有行为）"""
    connection = test_engine.connect()
    transaction = connection.begin()
    
    session = Session(bind=connection)
    session.execute(text("SET search_path TO public"))
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


# 测试工具函数
def print_rls_debug_info(session: Session, table_name: str):
    """打印RLS调试信息"""
    from tests.utils.db_setup import check_rls_status
    info = check_rls_status(session, table_name)
    print(f"\n🔍 RLS Debug Info for {table_name}:")
    print(f"  RLS Enabled: {info['rls_enabled']}")
    print(f"  Policies: {len(info['policies'])}")
    for policy in info['policies']:
        print(f"    - {policy['name']}: {policy['condition'][:100]}...")


def print_tenant_context(session: Session):
    """打印当前租户上下文"""
    try:
        current_tenant = session.exec(text(
            "SELECT current_setting('app.current_tenant_id', true)"
        )).first()
        print(f"\n🏢 Current Tenant Context: {current_tenant}")
    except Exception as e:
        print(f"\n❌ Failed to get tenant context: {e}")


def count_records_by_tenant(session: Session, table_name: str) -> dict:
    """按租户统计记录数（调试用）"""
    from tests.utils.db_setup import reset_tenant_context
    
    # 重置上下文以绕过RLS
    reset_tenant_context(session)
    
    try:
        # 查询所有记录的租户分布
        result = session.exec(text(f"""
            SELECT tenant_id, COUNT(*) as count 
            FROM {table_name} 
            GROUP BY tenant_id
        """)).all()
        
        return {str(tenant_id): count for tenant_id, count in result}
    except Exception as e:
        print(f"Failed to count records for {table_name}: {e}")
        return {}


# 使用示例的测试类
class TestRLSDebugging:
    """RLS调试测试示例"""
    
    def test_rls_status_check(self, test_session, rls_debug_info):
        """测试RLS状态检查"""
        if rls_debug_info:
            info = rls_debug_info["get_rls_info"]("member_fixed_slot_locks")
            print(f"RLS Info: {info}")
            assert info["rls_enabled"] is True
    
    def test_tenant_context_management(self, tenant_context_manager):
        """测试租户上下文管理"""
        with tenant_context_manager as ctx:
            ctx.set_tenant(1)
            # 在这个上下文中进行测试
            pass
        # 上下文自动重置
