"""数据库测试fixtures"""
import pytest
import os
from typing import Generator
from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy import text

# 测试数据库配置
TEST_POSTGRES_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
TEST_DATABASE_URL = TEST_POSTGRES_URL


@pytest.fixture(scope="session")
def test_engine():
    """创建测试数据库引擎"""
    connect_args = {}
    default_db_url = TEST_POSTGRES_URL.rsplit('/', 1)[0] + '/postgres'
    temp_engine = create_engine(default_db_url)
    with temp_engine.connect() as conn:
        conn.execute(text("COMMIT"))  # 确保不在事务中
        # 先删除已存在的测试数据库
        conn.execute(text("DROP DATABASE IF EXISTS course_booking_test_db"))
        # 创建新的测试数据库
        conn.execute(text("CREATE DATABASE course_booking_test_db"))
    temp_engine.dispose()

    engine = create_engine(
        TEST_DATABASE_URL,
        echo=False,
        connect_args=connect_args
    )
    
    # 直接复用正式环境的数据库创建逻辑
    from app.db.base import create_db_and_tables
    
    # 临时替换全局engine为测试engine
    import app.db.session
    original_engine = app.db.session.engine
    app.db.session.engine = engine
    
    try:
        create_db_and_tables()
    finally:
        # 恢复原始engine
        app.db.session.engine = original_engine
    
    yield engine
    
    # 清理测试数据库
    SQLModel.metadata.drop_all(engine)
    engine.dispose()
    default_db_url = TEST_POSTGRES_URL.rsplit('/', 1)[0] + '/postgres'
    temp_engine = create_engine(default_db_url)
    with temp_engine.connect() as conn:
        conn.execute(text("COMMIT"))
        conn.execute(text("DROP DATABASE IF EXISTS course_booking_test_db"))
    temp_engine.dispose()


@pytest.fixture
def test_session(test_engine) -> Generator[Session, None, None]:
    """创建测试数据库会话"""
    connection = test_engine.connect()
    transaction = connection.begin()
    
    session = Session(bind=connection)
    session.execute(text("SET search_path TO public"))
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close() 