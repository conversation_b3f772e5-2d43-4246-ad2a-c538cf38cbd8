"""会员固定课位锁定API集成测试"""
import pytest
from fastapi.testclient import TestClient
from app.api.common.exceptions import GlobalErrorCode
from app.features.members.fixed_lock_exceptions import MemberFixedSlotLockErrorCode
from app.features.members.fixed_lock_models import MemberFixedSlotLockStatus


class TestMemberFixedSlotLockBasicAPI:
    """会员固定课位锁定基础API测试"""

    def test_create_lock_success(self, client: TestClient, admin_token, created_member, created_fixed_slot):
        """测试成功创建固定课位锁定"""
        lock_data = {
            "member_id": created_member["id"],
            "teacher_fixed_slot_id": created_fixed_slot["id"],
            "status": "active",
            "created_by": created_member["created_by"]
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/",
            json=lock_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        
        # 验证响应格式
        assert data["success"] is True
        assert data["message"] == "固定课位锁定创建成功"
        assert "data" in data
        
        # 验证返回的数据
        lock = data["data"]
        assert lock["member_id"] == created_member["id"]
        assert lock["teacher_fixed_slot_id"] == created_fixed_slot["id"]
        assert lock["teacher_id"] == created_fixed_slot["teacher_id"]
        assert lock["weekday"] == created_fixed_slot["weekday"]
        assert lock["status"] == "active"
        assert "id" in lock
        assert "locked_at" in lock

    def test_create_lock_unauthorized(self, client: TestClient, created_member, created_fixed_slot):
        """测试未认证用户创建固定课位锁定"""
        lock_data = {
            "member_id": created_member["id"],
            "teacher_fixed_slot_id": created_fixed_slot["id"],
            "status": "active",
            "created_by": created_member["created_by"]
        }
        
        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data)
        
        assert response.status_code == 401

    def test_create_lock_member_not_found(self, client: TestClient, admin_token, created_fixed_slot):
        """测试创建锁定时会员不存在"""
        lock_data = {
            "member_id": 99999,  # 不存在的会员ID
            "teacher_fixed_slot_id": created_fixed_slot["id"],
            "status": "active",
            "created_by": 1
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/",
            json=lock_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["business_code"] == MemberFixedSlotLockErrorCode.MEMBER_NOT_FOUND

    def test_create_lock_slot_not_found(self, client: TestClient, admin_token, created_member):
        """测试创建锁定时教师固定时间段不存在"""
        lock_data = {
            "member_id": created_member["id"],
            "teacher_fixed_slot_id": 99999,  # 不存在的时间段ID
            "status": "active",
            "created_by": created_member["created_by"]
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/",
            json=lock_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["business_code"] == MemberFixedSlotLockErrorCode.TEACHER_SLOT_NOT_FOUND

    def test_create_lock_slot_already_locked(self, client: TestClient, admin_token, created_lock, created_member_2):
        """测试创建锁定时时间段已被锁定"""
        lock_data = {
            "member_id": created_member_2["id"],
            "teacher_fixed_slot_id": created_lock["teacher_fixed_slot_id"],  # 已被锁定的时间段
            "status": "active",
            "created_by": created_member_2["created_by"]
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/",
            json=lock_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["business_code"] == MemberFixedSlotLockErrorCode.SLOT_ALREADY_LOCKED

    def test_create_lock_invalid_data(self, client: TestClient, admin_token):
        """测试创建锁定时数据验证失败"""
        lock_data = {
            "member_id": "invalid",  # 无效的会员ID
            "teacher_fixed_slot_id": "invalid",  # 无效的时间段ID
            "status": "invalid_status",  # 无效状态
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/",
            json=lock_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 422


class TestMemberFixedSlotLockQueryAPI:
    """会员固定课位锁定查询API测试"""

    def test_get_lock_success(self, client: TestClient, admin_token, created_lock):
        """测试成功获取锁定详情"""
        response = client.get(
            f"/api/v1/admin/members/fixed-locks/{created_lock['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert "data" in data
        
        lock = data["data"]
        assert lock["id"] == created_lock["id"]
        assert lock["member_id"] == created_lock["member_id"]
        assert lock["teacher_fixed_slot_id"] == created_lock["teacher_fixed_slot_id"]
        assert lock["status"] == created_lock["status"]

    def test_get_lock_not_found(self, client: TestClient, admin_token):
        """测试获取不存在的锁定"""
        response = client.get(
            "/api/v1/admin/members/fixed-locks/99999",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 404

    def test_get_locks_list_success(self, client: TestClient, admin_token, created_multiple_locks):
        """测试成功获取锁定列表"""
        response = client.get(
            "/api/v1/admin/members/fixed-locks/",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert "data" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        
        locks = data["data"]
        assert len(locks) >= len(created_multiple_locks)

    def test_get_locks_list_with_member_filter(self, client: TestClient, admin_token, created_multiple_locks):
        """测试按会员筛选锁定列表"""
        member_id = created_multiple_locks[0]["member_id"]
        
        response = client.get(
            f"/api/v1/admin/members/fixed-locks/?member_id={member_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        locks = data["data"]
        for lock in locks:
            assert lock["member_id"] == member_id

    def test_get_locks_list_with_status_filter(self, client: TestClient, admin_token, created_multiple_locks):
        """测试按状态筛选锁定列表"""
        response = client.get(
            "/api/v1/admin/members/fixed-locks/?status=active",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        locks = data["data"]
        for lock in locks:
            assert lock["status"] == "active"

    def test_get_locks_list_with_pagination(self, client: TestClient, admin_token, created_multiple_locks):
        """测试分页查询锁定列表"""
        response = client.get(
            "/api/v1/admin/members/fixed-locks/?page=1&size=2",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["page"] == 1
        assert data["size"] == 2
        assert len(data["data"]) <= 2

    def test_get_member_locks_success(self, client: TestClient, admin_token, created_multiple_locks):
        """测试获取指定会员的锁定记录"""
        member_id = created_multiple_locks[0]["member_id"]
        
        response = client.get(
            f"/api/v1/admin/members/fixed-locks/member/{member_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        locks = data["data"]
        for lock in locks:
            assert lock["member_id"] == member_id

    def test_get_teacher_slot_locks_success(self, client: TestClient, admin_token, created_multiple_locks):
        """测试获取指定教师的时间段锁定情况"""
        teacher_id = created_multiple_locks[0]["teacher_id"]
        
        response = client.get(
            f"/api/v1/admin/members/fixed-locks/teacher/{teacher_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        locks = data["data"]
        for lock in locks:
            assert lock["teacher_id"] == teacher_id


class TestMemberFixedSlotLockStatusAPI:
    """会员固定课位锁定状态管理API测试"""

    def test_activate_lock_success(self, client: TestClient, admin_token, created_lock):
        """测试成功激活锁定"""
        response = client.post(
            f"/api/v1/admin/members/fixed-locks/{created_lock['id']}/activate",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert data["message"] == "锁定已激活"
        assert data["data"]["status"] == "active"

    def test_pause_lock_success(self, client: TestClient, admin_token, created_lock):
        """测试成功暂停锁定"""
        response = client.post(
            f"/api/v1/admin/members/fixed-locks/{created_lock['id']}/pause",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert data["message"] == "锁定已暂停"
        assert data["data"]["status"] == "paused"

    def test_cancel_lock_success(self, client: TestClient, admin_token, created_lock):
        """测试成功取消锁定"""
        response = client.post(
            f"/api/v1/admin/members/fixed-locks/{created_lock['id']}/cancel",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert data["message"] == "锁定已取消"
        assert data["data"]["status"] == "cancelled"

    def test_update_lock_status_success(self, client: TestClient, admin_token, created_lock):
        """测试通过状态接口更新锁定状态"""
        status_data = {
            "target_status": "paused",
            "reason": "测试暂停锁定"
        }
        
        response = client.post(
            f"/api/v1/admin/members/fixed-locks/{created_lock['id']}/status",
            json=status_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        result = data["data"]
        assert result["success"] is True
        assert result["lock_id"] == created_lock["id"]

    def test_update_lock_status_not_found(self, client: TestClient, admin_token):
        """测试更新不存在锁定的状态"""
        status_data = {
            "target_status": "paused",
            "reason": "测试"
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/99999/status",
            json=status_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 404

    def test_update_lock_status_invalid_status(self, client: TestClient, admin_token, created_lock):
        """测试更新为无效状态"""
        status_data = {
            "target_status": "invalid_status",
            "reason": "测试无效状态"
        }
        
        response = client.post(
            f"/api/v1/admin/members/fixed-locks/{created_lock['id']}/status",
            json=status_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 422


class TestMemberFixedSlotLockBatchAPI:
    """会员固定课位锁定批量操作API测试"""

    def test_batch_create_locks_success(self, client: TestClient, admin_token, created_member, created_multiple_fixed_slots):
        """测试成功批量创建锁定"""
        # 选择可用且可见的时间段
        available_slots = [
            slot["id"] for slot in created_multiple_fixed_slots
            if slot["is_available"] and slot["is_visible_to_members"]
        ][:3]  # 只选择前3个
        
        batch_data = {
            "member_id": created_member["id"],
            "teacher_fixed_slot_ids": available_slots,
            "status": "active"
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/batch",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["success"] is True
        result = data["data"]
        assert result["total_count"] == len(available_slots)
        assert result["success_count"] > 0
        assert len(result["success_ids"]) == result["success_count"]

    def test_batch_update_locks_success(self, client: TestClient, admin_token, created_multiple_locks):
        """测试成功批量更新锁定"""
        lock_ids = [lock["id"] for lock in created_multiple_locks]
        
        batch_data = {
            "lock_ids": lock_ids,
            "status": "paused"
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/batch/update",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        result = data["data"]
        assert result["total_count"] == len(lock_ids)
        assert result["success_count"] > 0

    def test_batch_update_status_success(self, client: TestClient, admin_token, created_multiple_locks):
        """测试成功批量更新状态"""
        lock_ids = [lock["id"] for lock in created_multiple_locks]
        
        batch_data = {
            "lock_ids": lock_ids,
            "target_status": "cancelled",
            "reason": "批量取消测试"
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/batch/status",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        print("test_batch_update_status_success:", response.json())
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        result = data["data"]
        assert result["total_count"] == len(lock_ids)
        assert result["success_count"] > 0

    def test_batch_delete_locks_success(self, client: TestClient, admin_token, created_multiple_locks):
        """测试成功批量删除锁定"""
        lock_ids = [lock["id"] for lock in created_multiple_locks]
        
        batch_data = {
            "lock_ids": lock_ids,
            "reason": "批量删除测试"
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/batch/delete",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        result = data["data"]
        assert result["total_count"] == len(lock_ids)
        assert result["success_count"] > 0

    def test_batch_create_locks_invalid_data(self, client: TestClient, admin_token, created_member):
        """测试批量创建锁定时数据验证失败"""
        batch_data = {
            "member_id": created_member["id"],
            "teacher_fixed_slot_ids": [],  # 空列表
            "status": "active"
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/batch",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 422

    def test_batch_update_locks_empty_ids(self, client: TestClient, admin_token):
        """测试批量更新锁定时ID列表为空"""
        batch_data = {
            "lock_ids": [],  # 空列表
            "status": "paused"
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/batch/update",
            json=batch_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 422


class TestMemberFixedSlotLockUtilityAPI:
    """会员固定课位锁定工具API测试"""

    def test_get_available_slots_success(self, client: TestClient, admin_token, created_teacher):
        """测试成功获取可锁定时间段"""
        response = client.get(
            f"/api/v1/admin/members/fixed-locks/available-slots?teacher_id={created_teacher['id']}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        slots = data["data"]
        
        for slot in slots:
            assert slot["teacher_id"] == created_teacher["id"]
            assert slot["is_available"] is True
            assert slot["is_visible_to_members"] is True

    def test_get_available_slots_with_weekday_filter(self, client: TestClient, admin_token, created_teacher):
        """测试按星期筛选可锁定时间段"""
        response = client.get(
            f"/api/v1/admin/members/fixed-locks/available-slots?teacher_id={created_teacher['id']}&weekdays=1,2,3",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        print(f"test_get_available_slots_with_weekday_filter: {response.json()}")
        assert response.status_code == 200
        data = response.json()
        
        slots = data["data"]
        for slot in slots:
            assert slot["weekday"] in [1, 2, 3]

    def test_get_available_slots_invalid_weekdays(self, client: TestClient, admin_token, created_teacher):
        """测试无效的星期筛选参数"""
        response = client.get(
            f"/api/v1/admin/members/fixed-locks/available-slots?teacher_id={created_teacher['id']}&weekdays=invalid",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        print("test_get_available_slots_invalid_weekdays", response.json())
        assert response.status_code == 400

    def test_check_lock_conflict_no_conflict(self, client: TestClient, admin_token, created_member, created_fixed_slot):
        """测试冲突检测 - 无冲突"""
        conflict_data = {
            "member_id": created_member["id"],
            "teacher_fixed_slot_id": created_fixed_slot["id"]
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/conflict-check",
            json=conflict_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        result = data["data"]
        assert result["has_conflict"] is False

    def test_check_lock_conflict_has_conflict(self, client: TestClient, admin_token, created_lock, created_member_2):
        """测试冲突检测 - 存在冲突"""
        conflict_data = {
            "member_id": created_member_2["id"],
            "teacher_fixed_slot_id": created_lock["teacher_fixed_slot_id"]  # 已被锁定的时间段
        }
        
        response = client.post(
            "/api/v1/admin/members/fixed-locks/conflict-check",
            json=conflict_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        result = data["data"]
        assert result["has_conflict"] is True
        assert result["conflict_type"] == "slot_already_locked"

    def test_delete_lock_success(self, client: TestClient, admin_token, created_lock):
        """测试成功删除锁定"""
        response = client.post(
            f"/api/v1/admin/members/fixed-locks/{created_lock['id']}/delete",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert data["message"] == "固定课位锁定删除成功"

    def test_delete_lock_not_found(self, client: TestClient, admin_token):
        """测试删除不存在的锁定"""
        response = client.post(
            "/api/v1/admin/members/fixed-locks/99999/delete",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 404


class TestMemberFixedSlotLockMultiTenantAPI:
    """会员固定课位锁定多租户隔离API测试"""

    def test_cross_tenant_isolation(self, client: TestClient, admin_token, second_tenant_admin_token, created_lock):
        """测试跨租户数据隔离"""
        # 第二个租户的管理员尝试访问第一个租户的锁定记录
        response = client.get(
            f"/api/v1/admin/members/fixed-locks/{created_lock['id']}",
            headers={"Authorization": f"Bearer {second_tenant_admin_token}"}
        )
        
        assert response.status_code == 404

    def test_tenant_specific_lock_list(self, client: TestClient, admin_token, created_multiple_locks, second_tenant_admin_token, test_session):
        """测试租户专属锁定列表"""
        from app.utils.security import verify_token
        from sqlalchemy import text

        # 解析两个token，检查租户信息
        token1_data = verify_token(admin_token)
        token2_data = verify_token(second_tenant_admin_token)
        print(f"Token1 data: {token1_data}")
        print(f"Token2 data: {token2_data}")

        # 检查数据库中的实际数据（不使用RLS）
        test_session.exec(text("RESET app.current_tenant_id"))
        all_locks = test_session.exec(text("SELECT id, tenant_id FROM member_fixed_slot_locks")).all()
        print(f"All locks in database: {all_locks}")

        # 检查RLS是否启用
        rls_enabled = test_session.exec(text("SELECT relrowsecurity FROM pg_class WHERE relname = 'member_fixed_slot_locks'")).first()
        print(f"RLS enabled for member_fixed_slot_locks: {rls_enabled}")

        # 检查RLS策略
        policies = test_session.exec(text("SELECT policyname, qual FROM pg_policies WHERE tablename = 'member_fixed_slot_locks'")).all()
        print(f"RLS policies: {policies}")

        # 测试RLS隔离
        test_session.exec(text("SET app.current_tenant_id = '1'"))
        result1 = test_session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        print(f"Tenant 1 locks count (with RLS): {result1}")

        test_session.exec(text("SET app.current_tenant_id = '2'"))
        result2 = test_session.exec(text("SELECT COUNT(*) FROM member_fixed_slot_locks")).first()
        print(f"Tenant 2 locks count (with RLS): {result2}")

        # 检查当前租户上下文
        current_tenant = test_session.exec(text("SELECT current_setting('app.current_tenant_id', true)")).first()
        print(f"Current tenant context: {current_tenant}")

        # 第一个租户的管理员查看锁定列表
        response1 = client.get(
            "/api/v1/admin/members/fixed-locks/",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        # 第二个租户的管理员查看锁定列表
        response2 = client.get(
            "/api/v1/admin/members/fixed-locks/",
            headers={"Authorization": f"Bearer {second_tenant_admin_token}"}
        )

        assert response1.status_code == 200
        assert response2.status_code == 200

        json1 = response1.json()
        json2 = response2.json()
        data1 = json1["data"]
        data2 = json2["data"]
        print("test_tenant_specific_lock_list response1.json():", json1)
        print("test_tenant_specific_lock_list response2.json():", json2)
        print("test_tenant_specific_lock_list 1 total:", json1["total"])
        print("test_tenant_specific_lock_list 2 total:", json2["total"])

        # 第二个租户应该看不到第一个租户的数据
        assert json2["total"] == 0, f"第二个租户应该看到0条记录，但实际看到了{json2['total']}条"
        assert len(data2) == 0, f"第二个租户的数据列表应该为空，但实际有{len(data2)}条记录"

        # 第一个租户应该能看到自己的数据
        assert json1["total"] > 0, f"第一个租户应该能看到自己的数据，但实际看到了{json1['total']}条"