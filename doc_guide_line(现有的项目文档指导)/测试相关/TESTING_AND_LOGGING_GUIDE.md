# 测试调试和日志管理完整指南

## 🎯 概述

本指南提供了一套完整的测试调试和日志管理解决方案，解决以下问题：

- 测试输出过于冗长，关键错误信息被淹没
- 缺乏结构化的测试日志记录
- 调试失败测试时信息不足
- 生产环境日志管理不规范

## 🚀 快速开始

### 1. 使用增强测试脚本

```bash
# 运行单个测试并保存日志
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py::TestMemberProfileAPI::test_update_my_profile

# 运行测试模块
python scripts/test_debug.py tests/unit/features/members/

# 不过滤日志（查看完整输出）
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py --no-filter

# 使用长格式traceback
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py --tb=long

# 自定义日志文件名
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py --log-name=member_profile_debug
```

### 2. 查看测试日志

```bash
# 日志文件位置
ls logs/tests/

# 查看最新的测试日志
tail -f logs/tests/test_profile_20250107_112030.log

# 搜索错误信息
grep -n "ERROR\|FAILED\|Exception" logs/tests/test_profile_20250107_112030.log
```

## 📋 测试工作流程最佳实践

### 1. 日常开发测试

```bash
# 步骤1: 运行测试并保存日志
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py

# 步骤2: 如果失败，查看过滤后的日志
cat logs/tests/test_profile_*.log

# 步骤3: 如果需要更多信息，运行完整日志
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py --no-filter

# 步骤4: 修复问题后重新测试
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py --log-name=fixed_attempt
```

### 2. 调试复杂问题

```bash
# 使用详细traceback
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py --tb=long --no-filter

# 运行相关的测试套件
python scripts/test_debug.py tests/integration/api/v1/member/ --log-name=member_module_debug

# 对比不同版本的日志
diff logs/tests/before_fix_*.log logs/tests/after_fix_*.log
```

## 🔧 在测试代码中使用日志

### 1. 基础用法

```python
from tests.utils.logging_helper import LoggedTestCase, log_test_info, log_test_debug

class TestMemberAPI(LoggedTestCase):
    def test_create_member(self, client, admin_token):
        """测试创建会员"""
        # 记录测试步骤
        self.log_step("准备测试数据")

        member_data = {
            "name": "测试会员",
            "phone": "13800138000",
            "email": "<EMAIL>"
        }

        # 记录测试数据
        self.log_data("member_data", member_data)

        # 记录API调用
        self.log_step("发送创建会员请求")
        response = client.post("/api/v1/admin/members/", json=member_data)

        self.log_api("POST", "/api/v1/admin/members/", member_data, response.json())

        # 断言并记录
        self.assert_and_log(
            response.status_code == 201,
            "创建会员应该返回201状态码",
            expected=201,
            actual=response.status_code
        )
```

### 2. 使用装饰器

```python
from tests.utils.logging_helper import log_test_method

class TestMemberService:
    @log_test_method
    def test_update_member(self, test_session, created_member):
        """测试更新会员信息"""
        # 测试逻辑...
        pass
```

### 3. 上下文管理器

```python
from tests.utils.logging_helper import test_logging_context

def test_complex_scenario():
    with test_logging_context("complex_member_scenario") as logger:
        logger.info("开始复杂场景测试")
        # 测试逻辑...
        logger.info("场景测试完成")
```

## 📊 日志系统配置

### 1. 环境配置

```python
# 开发环境 - 详细日志到控制台和文件
setup_logging(
    environment="development",
    log_level="DEBUG",
    enable_file_logging=True,
    filter_sqlalchemy=False
)

# 测试环境 - 过滤数据库日志
setup_logging(
    environment="testing",
    log_level="INFO",
    enable_file_logging=True,
    filter_sqlalchemy=True
)

# 生产环境 - 简化日志
setup_logging(
    environment="production",
    log_level="WARNING",
    enable_file_logging=True,
    filter_sqlalchemy=True
)
```

### 2. 应用中使用日志

```python
from app.core.logging import get_logger, log_business_event, log_error

# 获取日志器
logger = get_logger("app.members")

# 记录业务事件
log_business_event("会员创建", {
    "member_id": member.id,
    "tenant_id": tenant_id,
    "created_by": user_id
})

# 记录错误
try:
    # 业务逻辑
    pass
except Exception as e:
    log_error(e, {
        "operation": "create_member",
        "tenant_id": tenant_id
    })
    raise
```

## 📁 日志文件组织

```
logs/
├── app.log              # 应用主日志
├── error.log            # 错误日志
├── test.log             # 测试日志
└── tests/               # 测试专用日志目录
    ├── test_profile_20250107_112030.log
    ├── test_member_service_20250107_113045.log
    └── member_module_debug_20250107_114500.log
```

## 🔍 日志分析技巧

### 1. 快速定位错误

```bash
# 查找所有错误
grep -r "ERROR\|FAILED\|Exception" logs/

# 查找特定测试的错误
grep -A 5 -B 5 "test_update_my_profile" logs/tests/test_profile_*.log

# 查找API调用失败
grep -A 3 "API调用.*POST" logs/tests/test_profile_*.log
```

### 2. 性能分析

```bash
# 查找慢查询
grep -E "SELECT.*[0-9]+\.[0-9]+s" logs/app.log

# 查找长时间运行的测试
grep -E "测试完成.*[0-9]+\.[0-9]+s" logs/tests/
```

### 3. 业务事件追踪

```bash
# 追踪特定会员的操作
grep "member_id.*123" logs/app.log

# 追踪特定租户的活动
grep "tenant_id.*456" logs/app.log
```

## ⚡ 性能优化建议

### 1. 日志级别控制

```python
# 生产环境只记录WARNING及以上级别
if settings.DEBUG:
    log_level = "DEBUG"
else:
    log_level = "WARNING"
```

### 2. 异步日志（高并发场景）

```python
# 使用队列处理器避免阻塞
handlers["async_file"] = {
    "class": "logging.handlers.QueueHandler",
    "queue": queue.Queue(),
    "target": {
        "class": "logging.handlers.RotatingFileHandler",
        "filename": "logs/app.log"
    }
}
```

### 3. 日志轮转配置

```python
# 按大小轮转
"maxBytes": 10485760,  # 10MB
"backupCount": 5,

# 按时间轮转
"class": "logging.handlers.TimedRotatingFileHandler",
"when": "midnight",
"interval": 1,
"backupCount": 30
```

## 🚨 故障排查指南

### 1. 测试失败排查

1. **查看过滤后的日志** - 快速定位问题
2. **查看完整日志** - 了解详细上下文
3. **对比成功/失败日志** - 找出差异
4. **检查 fixture 依赖** - 确认测试数据正确

### 2. 常见问题解决

```bash
# 问题1: fixture不存在
grep "fixture.*not found" logs/tests/test_*.log

# 问题2: 数据库连接问题
grep -i "database\|connection" logs/tests/test_*.log

# 问题3: 权限问题
grep -i "permission\|unauthorized" logs/tests/test_*.log

# 问题4: 参数错误
grep -i "unexpected keyword\|missing.*argument" logs/tests/test_*.log
```

## 📈 监控和告警

### 1. 日志监控

```bash
# 监控错误日志
tail -f logs/error.log | grep -E "CRITICAL|ERROR"

# 监控测试失败
tail -f logs/tests/test_*.log | grep "FAILED"
```

### 2. 自动化分析

```python
# 分析测试结果脚本
def analyze_test_logs():
    log_dir = Path("logs/tests")
    for log_file in log_dir.glob("test_*.log"):
        with open(log_file) as f:
            content = f.read()
            if "FAILED" in content:
                print(f"失败的测试: {log_file.name}")
```

## 📊 解决方案总结

这个完整的解决方案提供了：

### ✅ 已实现的功能

1. **🔧 增强的测试脚本** (`scripts/test_debug.py`)

   - 支持输出重定向到文件
   - 智能过滤数据库和警告日志
   - 自定义日志文件名和 traceback 样式
   - 完整和过滤后的双重输出

2. **📝 统一日志系统** (`app/core/logging.py`)

   - 支持开发、测试、生产环境
   - 自动日志轮转和分级记录
   - SQLAlchemy 日志过滤
   - JSON 格式化器支持

3. **🧪 测试专用日志工具** (`tests/utils/logging_helper.py`)

   - 测试步骤详细记录
   - API 调用和断言日志
   - 测试上下文管理器
   - 基础测试类支持

这个完整的解决方案提供了：

- 🔧 **增强的测试脚本** - 支持输出重定向和过滤
- 📝 **结构化日志系统** - 适用于开发、测试、生产环境
- 🧪 **测试专用日志工具** - 详细记录测试过程
- 📋 **最佳实践指南** - 涵盖日常使用和故障排查
- ⚡ **即用工具** - 快速脚本和便捷命令
