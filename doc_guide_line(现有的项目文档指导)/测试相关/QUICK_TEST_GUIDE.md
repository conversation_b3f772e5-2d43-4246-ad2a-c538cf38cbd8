# 测试开发快速指南

## 目录结构

本项目采用分层的测试架构，按照测试类型和职责进行组织：

```
tests/
├── conftest.py                    # 全局配置和基础fixtures
├── fixtures/                     # 分类的fixture文件
│   ├── database.py               # 数据库相关fixtures
│   ├── client.py                 # API客户端fixtures
│   ├── auth.py                   # 认证相关fixtures （尚未创建文件，看需要是否创建）
│   └── business/                 # 业务数据fixtures
│       ├── tenant.py             # 租户相关fixtures
│       ├── user.py               # 用户相关fixtures
│       └── member.py             # 会员相关fixtures
├── unit/                         # 单元测试
│   ├── services/                 # 服务层测试
│   │   ├── test_tenant_service.py # 租户相关service层测试
│   │   ├── test_user_service.py   # 用户相关service层测试
│   │   └── test_member_service.py # 会员相关service层测试
│   └── utils/                    # 工具函数测试
│       └── test_security.py      # （尚未创建文件，看需要是否创建）
├── integration/                  # 集成测试
│   ├── api/                      # API集成测试
│   │   ├── v1/
│   │   │   ├── test_tenant_api.py # 租户相关api层测试
│   │   │   ├── test_user_api.py   # 用户相关api层测试
│   │   │   ├── test_member_api.py # 会员相关api层测试
│   │   └── xxx.py           # API测试专用fixtures （未创建文件，看需要是否创建）
├── e2e/                          # 端到端测试 日常开发不用实现，小版本封版时候统一做
│   ├── scenarios/
│   │   ├── test_manual_scenarios.py
│   └── conftest.py               # E2E测试专用fixtures （未创建文件，看需要是否创建）
└── performance/                  # 性能测试（暂不实现）
    ├── test_api_performance.py   # （暂不实现，未创建文件）
    └── conftest.py               # （暂不实现，未创建文件）
```

## 🚀 快速开始

### 测试命名规则

```python
def test_{操作}_{条件}_{期望结果}():
    """简短描述测试目的"""
```

### AAA 测试结构

```python
def test_create_user_should_return_user():
    # Arrange - 准备数据
    user_data = UserCreate(email="<EMAIL>", real_name="测试")

    # Act - 执行操作
    result = user_service.create_user(db, user_data, tenant_id)

    # Assert - 验证结果
    assert result.email == user_data.email
```

## 📝 单元测试模板

### Service 层测试

```python
class TestUserService:
    def test_create_user_with_valid_data_should_return_user(self, db_session, sample_tenant):
        # 准备
        user_data = UserCreate(email="<EMAIL>", real_name="测试")

        # 执行
        result = user_service.create_user(db_session, user_data, sample_tenant.id)

        # 验证
        assert result is not None
        assert result.email == user_data.email
        assert result.tenant_id == sample_tenant.id

    def test_create_user_with_duplicate_email_should_raise_error(self, db_session, sample_tenant):
        # 准备 - 先创建一个用户
        existing_data = UserCreate(email="<EMAIL>", real_name="已存在")
        user_service.create_user(db_session, existing_data, sample_tenant.id)

        # 执行 & 验证 - 创建重复邮箱用户应该抛出异常
        duplicate_data = UserCreate(email="<EMAIL>", real_name="重复")
        with pytest.raises(HTTPException) as exc:
            user_service.create_user(db_session, duplicate_data, sample_tenant.id)
        assert exc.value.status_code == 400
```

## 🌐 API 测试模板

### 基础 API 测试

```python
def test_create_user_api_should_return_201(client, sample_tenant):
    # 准备
    user_data = {
        "email": "<EMAIL>",
        "real_name": "测试用户",
        "password": "password123"
    }

    # 执行
    response = client.post(
        "/api/v1/users/",
        json=user_data,
        params={"tenant_id": sample_tenant.id}
    )

    # 验证
    assert response.status_code == 201
    data = response.json()
    assert data["email"] == user_data["email"]
    assert "password" not in data  # 密码不应返回
```

## 🔧 常用 Fixtures

### 基础数据 Fixtures

```python
@pytest.fixture
def sample_user_data():
    return UserCreate(
        email="<EMAIL>",
        real_name="测试用户",
        password="password123"
    )

@pytest.fixture
def created_user(db_session, sample_tenant, sample_user_data):
    return user_service.create_user(
        db=db_session,
        user_create=sample_user_data,
        tenant_id=sample_tenant.id
    )
```

## ✅ 必测场景清单

### CRUD 操作

- [ ] 创建：有效数据 → 成功
- [ ] 创建：重复数据 → 失败
- [ ] 查询：存在 ID → 返回数据
- [ ] 查询：不存在 ID → 返回 None/404
- [ ] 更新：有效数据 → 成功
- [ ] 删除：存在 ID → 成功

### API 端点

- [ ] 成功响应：正确状态码和数据格式
- [ ] 错误响应：适当的错误码和错误信息
- [ ] 数据验证：无效输入 → 422 错误

## 🚨 常见错误避免

### 数据隔离

```python
# ❌ 错误 - 可能影响其他测试
def test_create_user():
    user = User(email="<EMAIL>")  # 直接创建，可能冲突

# ✅ 正确 - 使用唯一数据
def test_create_user():
    unique_email = f"test_{uuid4()}@example.com"
    user = User(email=unique_email)
```

### 断言明确性

```python
# ❌ 错误 - 断言不够明确
assert user  # 只检查是否存在

# ✅ 正确 - 明确验证属性
assert user is not None
assert user.email == expected_email
assert user.status == "active"
```

### 404 错误调试

```python
# ❌ 错误 - 直接测试更新，可能因为数据不存在而404
def test_update_config_success(self, client, admin_token):
    response = client.post("/api/v1/courses/config/update", ...)
    # 如果404，不知道是路由问题还是配置不存在

# ✅ 正确 - 先创建数据再测试
def test_update_config_success(self, client, admin_token, default_course_config):
    # 使用fixture确保配置存在
    response = client.post("/api/v1/courses/config/update", ...)
    assert response.status_code == 200
```

### Fixture 依赖缺失

```python
# ❌ 错误 - 使用不存在的fixture
def test_multi_tenant_isolation(self, client, admin_token, second_tenant_admin_token):
    # second_tenant_admin_token fixture不存在，测试会失败

# ✅ 正确 - 先创建需要的fixture
@pytest.fixture
def second_tenant_admin_token(client, created_second_tenant_admin):
    login_data = {
        "email": created_second_tenant_admin["email"],
        "password": created_second_tenant_admin["password"],
        "tenant_code": created_second_tenant_admin["tenant_code"]
    }
    response = client.post("/api/v1/auth/admin/login", json=login_data)
    return response.json()["data"]["access_token"]
```

### API 参数类型问题

```python
# ❌ 错误 - 查询参数传递复杂类型
def test_update_field_success(self, client, admin_token):
    response = client.post(
        "/api/v1/courses/config/field",
        params={
            "field_name": "duration",
            "field_value": 75  # 数字会被转换为字符串"75"
        }
    )

# ✅ 正确 - 使用请求体传递复杂类型
def test_update_field_success(self, client, admin_token):
    response = client.post(
        "/api/v1/courses/config/field",
        json={
            "field_name": "duration",
            "field_value": 75  # 保持原始类型
        }
    )
```

## 🎯 测试覆盖原则

### 优先级

1. **核心业务逻辑** - 必须 100%覆盖
2. **错误处理** - 重要异常场景
3. **边界条件** - 空值、极值、边界值
4. **集成点** - 模块间交互

### 快速检查

```bash
# 运行单元测试
python scripts/test.py unit

# 运行特定模块
python scripts/test.py unit -k "user"

# 跳过认证测试
python scripts/test.py api -k "not auth"
```

## 🔍 调试测试失败

### 常见失败原因排查

1. **404 错误**：

   - 检查路由是否正确注册
   - 确认测试数据是否存在
   - 验证权限和租户上下文

2. **422 验证错误**：

   - 检查请求数据格式
   - 确认字段类型匹配
   - 验证必填字段是否提供

3. **Fixture 未找到**：

   - 确认 fixture 名称拼写正确
   - 检查 fixture 是否已定义
   - 验证 fixture 的依赖关系

4. **数据隔离问题**：
   - 使用唯一标识符避免冲突
   - 确保测试间数据独立
   - 检查事务回滚是否正确

## 📋 提交前检查清单

- [ ] 测试命名清晰描述意图
- [ ] 使用 AAA 结构组织测试
- [ ] 包含成功和失败场景
- [ ] 断言具体且明确
- [ ] 数据隔离，不影响其他测试
- [ ] 所有新增测试都通过
- [ ] 相关的旧测试仍然通过
- [ ] 必要的 fixture 已创建
- [ ] 多租户隔离测试已包含
