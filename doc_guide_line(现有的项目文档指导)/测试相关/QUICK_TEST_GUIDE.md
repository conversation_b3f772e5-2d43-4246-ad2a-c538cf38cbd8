# 快速测试指南

## 🚀 常用测试命令

### 推荐方式 (带日志调试)

```bash
# 运行单个测试 (自动生成双重日志)
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py::TestMemberProfileAPI::test_update_my_profile

# 运行测试模块
python scripts/test_debug.py tests/unit/features/members/

# 自定义日志文件名
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py --log-name=member_debug

# 显示完整输出 (包含SQL日志)
python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py --no-filter
```

### 传统方式

```bash
# 运行所有单元测试
python scripts/test.py unit

# 运行所有API集成测试
python scripts/test.py api

# 运行特定模块测试
python scripts/test.py unit -k "member"
python scripts/test.py api -k "profile"
```
    └── performance/              # 性能测试（暂不实现）
```

## 📋 查看测试结果

### 日志文件

```bash
# 查看过滤后的日志 (推荐)
cat logs/tests/test_name_*_filtered.log

# 查看完整日志 (调试时)
cat logs/tests/test_name_*_full.log

# 搜索错误信息
grep -n "ERROR\|FAILED\|Exception" logs/tests/test_name_*_filtered.log
```

### 环境变量控制

```bash
# 禁用 SQL 日志 (默认)
SQLALCHEMY_ECHO=false python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py

# 启用 SQL 日志 (调试时)
SQLALCHEMY_ECHO=true python scripts/test_debug.py tests/integration/api/v1/member/test_profile.py
```

## 📝 测试编写要点

### 命名规则
```python
def test_{操作}_{条件}_{期望结果}():
    """简短描述测试目的"""
```

### AAA 结构
```python
def test_create_user_should_return_user():
    # Arrange - 准备数据
    user_data = UserCreate(email="<EMAIL>")

    # Act - 执行操作
    result = user_service.create_user(db, user_data, tenant_id)

    # Assert - 验证结果
    assert result.email == user_data.email
```

## 🔧 常用 Fixtures

```python
# 数据库会话
db_session

# 测试客户端
client

# 认证相关
admin_token, member_token

# 业务数据
created_tenant, created_admin_user, created_member
```

## ✅ 测试要点

### CRUD 基本场景
- 创建：有效数据 → 成功，重复数据 → 失败
- 查询：存在 ID → 返回数据，不存在 ID → 404
- 更新：有效数据 → 成功
- 删除：存在 ID → 成功

### API 响应验证
- 成功响应：正确状态码和数据格式
- 错误响应：适当的错误码和错误信息
- 数据验证：无效输入 → 422 错误

## 🚨 常见问题

### 测试失败排查
1. **404 错误**: 检查路由注册、测试数据存在性、权限设置
2. **422 验证错误**: 检查请求数据格式、字段类型、必填字段
3. **Fixture 未找到**: 确认 fixture 名称、定义、依赖关系
4. **数据隔离问题**: 使用唯一标识符、确保测试独立

### 避免常见错误
- 使用唯一数据避免测试间冲突
- 明确断言具体属性而非仅检查存在性
- 先创建测试数据再执行操作
- 使用 JSON 而非查询参数传递复杂类型

## 📋 快速检查

```bash
# 运行特定模块
python scripts/test.py unit -k "user"
python scripts/test.py api -k "member"

# 跳过特定测试
python scripts/test.py api -k "not auth"
```

**详细指南**: 参考 `TESTING_AND_LOGGING_COMPLETE_GUIDE.md` 和 `TEST_CASE_GUIDELINES_V1.md`
