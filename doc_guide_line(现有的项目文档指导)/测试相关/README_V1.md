# 测试系统说明

## 🚀 快速开始

### 运行测试

```bash
# 运行所有单元测试
python scripts/test.py unit

# 运行所有API集成测试
python scripts/test.py api

# 运行所有测试
python scripts/test.py all

# 运行特定模块测试
python scripts/test.py unit -k "teacher"
python scripts/test.py api -k "tag"

# 运行特定功能测试
python scripts/test.py unit -k "service"  # 只运行服务层测试
python scripts/test.py api -k "create"    # 只运行创建相关API测试

# 生成覆盖率报告
python scripts/test.py unit --cov
python scripts/test.py api --cov

# 详细输出
python scripts/test.py unit -v
python scripts/test.py api -v -s

# 查看帮助
python scripts/test.py help
```

## 📁 目录结构

```
tests/
├── README_V1.md                 # 本文件
├── QUICK_TEST_GUIDE.md          # 快速测试指南
├── conftest.py                  # 全局配置
├── fixtures/                   # 测试夹具
│   ├── database.py             # 数据库fixtures
│   ├── client.py               # API客户端fixtures
│   └── business/               # 业务数据fixtures
│       ├── tenant_fixtures.py  # 租户测试数据
│       ├── user_fixtures.py    # 用户测试数据
│       ├── member_fixtures.py  # 会员测试数据
│       └── teacher_fixtures.py # 教师测试数据
├── unit/                       # 单元测试
│   ├── features/               # 业务模块测试
│   │   ├── tenants/            # 租户模块测试
│   │   ├── users/              # 用户模块测试
│   │   ├── members/            # 会员模块测试
│   │   ├── teachers/           # 教师模块测试
│   │   ├── tags/               # 标签模块测试
│   │   └── courses/            # 课程模块测试
│   ├── services/               # 服务层测试
│   └── utils/                  # 工具函数测试
├── integration/                # 集成测试
│   ├── api/v1/                 # API v1 测试
│   │   ├── test_tenant_api.py  # 租户API测试
│   │   ├── test_user_api.py    # 用户API测试
│   │   ├── test_member_api.py  # 会员API测试
│   │   ├── test_teacher_api.py # 教师API测试
│   │   └── test_tag_api.py     # 标签API测试
│   └── database/               # 数据库集成测试
├── e2e/                        # 端到端测试
└── performance/                # 性能测试
```

## 📊 当前状态

### ✅ 已完成模块

- **Tenant 模块**: 18 个测试全部通过 ✅
- **User 模块**: 34 个测试全部通过（service + API）✅
- **Member 模块**: 22 个测试全部通过（service + API）✅
- **Teacher 模块**: 完整的 service + API 测试 ✅
- **Tag 模块**: 34 个测试全部通过（service + API）✅
- **Course Config 模块**: 完整的配置管理测试 ✅
- **认证系统**: JWT 认证和权限测试 ✅
- **测试架构**: 分层组织，fixture 模块化 ✅

### 🚧 开发中模块

- **Fixed Slots 模块**: 教师固定时间占位（开发中）
- **Member Fixed Slot Locks**: 会员固定课位锁定（开发中）

### 📈 测试覆盖统计

- **单元测试**: 150+ 个测试用例
- **API 集成测试**: 100+ 个测试用例
- **测试覆盖率**: 90%+ 代码覆盖
- **测试通过率**: 100% 通过

### 💡 注意事项

- 默认使用 PostgreSQL 数据库（支持 RLS 行级安全）
- 所有测试用例都支持多租户隔离
- 使用统一的测试脚本 `python scripts/test.py`

## 📖 开发指南

### 日常开发

参考: [tests/QUICK_TEST_GUIDE.md](./QUICK_TEST_GUIDE.md)

### 测试重构

参考: [tests/TEST_REVIEW_GUIDE.md](./TEST_REVIEW_GUIDE.md)

### 完整规范

参考: [tests/TEST_CASE_GUIDELINES_V1.md](./TEST_CASE_GUIDELINES_V1.md)

## 🎯 测试最佳实践

### 编写测试的原则

1. **测试先行**: 先写服务层测试，再写 API 测试
2. **独立性**: 每个测试用例相互独立，不依赖执行顺序
3. **可重复**: 测试结果稳定，多次运行结果一致
4. **快速反馈**: 单元测试快速执行，集成测试适度
5. **覆盖全面**: 正常流程、异常流程、边界条件

### 测试数据管理

```python
# 使用 fixtures 创建测试数据
@pytest.fixture
def created_teacher(db_session, created_admin_user):
    """创建测试教师"""
    teacher_data = {
        "name": "Test Teacher",
        "email": "<EMAIL>",
        "phone": "13800138000"
    }
    # 使用 created_admin_user 而不是 created_tenant
    # 因为创建者应该是用户，租户信息从用户上下文获取
    return teacher_service.create_teacher(teacher_data)

# 测试用例中使用
def test_get_teacher(created_teacher):
    teacher = teacher_service.get_teacher(created_teacher.id)
    assert teacher.name == "Test Teacher"
```

### 异常测试模式

```python
def test_create_teacher_email_exists(created_teacher):
    """测试邮箱重复异常"""
    with pytest.raises(TeacherBusinessException) as exc_info:
        teacher_service.create_teacher({
            "name": "Another Teacher",
            "email": created_teacher.email  # 重复邮箱
        })

    assert exc_info.value.business_code == TeacherErrorCode.EMAIL_EXISTS
```

### API 测试模式

```python
def test_create_teacher_api(client, auth_headers):
    """测试创建教师API"""
    teacher_data = {
        "name": "API Test Teacher",
        "email": "<EMAIL>"
    }

    response = client.post(
        "/api/v1/teachers",
        json=teacher_data,
        headers=auth_headers
    )

    assert response.status_code == 200
    data = response.json()
    assert data["http_code"] == 200
    assert data["business_code"] == "SUCCESS"
    assert data["data"]["name"] == teacher_data["name"]
```

## 🔧 工具脚本

### scripts/test.py

统一的测试入口脚本，支持：

- 单元测试、API 测试、集成测试
- 灵活的参数传递

### scripts/test_tenants_manual.py

简单的手动 API 测试（需要服务器运行），暂时忽略，以后再说

### scripts/test_enhanced.py

增强版测试脚本，提供更多选项和功能。
