# 测试架构说明

## 目录结构

本项目采用分层的测试架构，按照测试类型和职责进行组织：

```
tests/
├── conftest.py                    # 全局配置和基础fixtures
├── fixtures/                     # 分类的fixture文件
│   ├── database.py               # 数据库相关fixtures
│   ├── client.py                 # API客户端fixtures
│   ├── auth.py                   # 认证相关fixtures （有问题，需重构）
│   └── business/                 # 业务数据fixtures
│       ├── tenant.py             # 租户相关fixtures
│       ├── user.py               # 用户相关fixtures
│       └── member.py             # 会员相关fixtures
├── unit/                         # 单元测试
│   ├── services/                 # 服务层测试
│   │   ├── test_tenant_service.py
│   │   ├── test_user_service.py
│   │   └── test_member_service.py
│   ├── models/                   # 模型测试
│   │   └── test_models.py
│   └── utils/                    # 工具函数测试
│       └── test_security.py
├── integration/                  # 集成测试
│   ├── api/                      # API集成测试
│   │   ├── v1/
│   │   │   ├── test_tenant_api.py
│   │   │   ├── test_user_api.py
│   │   │   ├── test_member_api.py
│   │   │   └── test_auth_api.py  （有问题，可能需重构）
│   │   └── conftest.py           # API测试专用fixtures （目前不需要，暂不实现，将来可能需要，未创建文件）
├── e2e/                          # 端到端测试
│   ├── scenarios/
│   │   ├── test_user_journey.py
│   │   └── test_tenant_lifecycle.py
│   └── conftest.py               # E2E测试专用fixtures （暂不实现，未创建文件）
└── performance/                  # 性能测试（暂不实现）
    ├── test_api_performance.py   # （暂不实现，未创建文件）
    └── conftest.py               # （暂不实现，未创建文件）
```

## 测试脚本

### 基础测试脚本

**scripts/test.py** - 主要测试脚本，支持多种测试模式：

```bash
# 运行所有单元测试
python scripts/test.py unit

# 运行所有API集成测试
python scripts/test.py api

# 运行所有测试
python scripts/test.py all

# 运行特定模块测试
python scripts/test.py unit -k "user"
python scripts/test.py api -k "not auth"

```

**scripts/test_enhanced.py** - 增强版测试脚本，支持更多选项：

```bash
# 快速测试（仅单元测试）
python scripts/test_enhanced.py quick

# 完整测试
python scripts/test_enhanced.py full

# 仅运行失败的测试
python scripts/test_enhanced.py failed

# 生成覆盖率报告
python scripts/test_enhanced.py coverage

# 详细模式
python scripts/test_enhanced.py unit --verbose
```

## 测试工具脚本

**scripts/test_tenants_manual.py** - 手动 HTTP 测试脚本：

- 独立的 HTTP 请求测试
- 可以直接测试运行中的 API 服务器
- 不依赖 pytest 框架

## 环境配置

### 数据库配置

测试只支持 PostgreSQL 数据库：

1. **PostgreSQL** (默认)：

   ```
   DATABASE_URL=postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db
   ```

### 环境变量

测试脚本会自动设置以下环境变量：

- `SECRET_KEY`: 测试用密钥
- `DEBUG`: 调试模式

## 测试数据管理

### Fixtures 组织

- **database.py**: 数据库连接、事务管理、数据清理
- **client.py**: FastAPI 测试客户端
- **business/**: 业务相关的测试数据工厂

### 数据隔离

- 每个测试用例使用独立的数据库事务
- 测试结束后自动回滚，保证数据隔离
- 支持多租户数据隔离测试

## 测试类型说明

### 单元测试 (unit/)

- **模型测试**: 验证数据模型的定义和约束
- **服务测试**: 验证业务逻辑的正确性
- 不依赖外部服务，使用 mock 或 stub

### 集成测试 (integration/)

- **API 测试**: 验证 HTTP 接口的功能
- 测试完整的请求-响应流程
- 包含数据验证和错误处理

### 端到端测试 (e2e/)

- **业务场景测试**: 模拟真实用户操作流程
- 跨模块的功能验证
- 完整的业务流程测试

## 最佳实践

### 测试命名

- 测试文件：`test_*.py`
- 测试类：`Test*`
- 测试方法：`test_*`

### 测试组织

- 按功能模块组织测试
- 一个测试类对应一个被测试的类
- 相关的测试用例放在同一个测试类中

### 数据准备

- 使用 fixtures 准备测试数据
- 优先使用工厂模式创建测试对象
- 保持测试数据的最小化和可读性

### 断言规范

- 使用明确的断言消息
- 一个测试用例验证一个具体的行为
- 避免过于复杂的测试逻辑

## 常见问题

### 数据库连接问题

如果遇到 PostgreSQL 连接问题，可以：

1. 检查数据库服务是否启动
2. 确认连接参数是否正确

### 测试隔离问题

如果测试之间存在数据污染：

1. 检查是否正确使用了事务 fixtures
2. 确认测试数据的清理逻辑
3. 使用`--tb=short`查看详细错误信息

### 性能问题

如果测试运行缓慢：

1. 只运行修改相关的测试

## 持续集成

推荐的 CI/CD 流程：

1. 单元测试（快速反馈）
2. 集成测试（功能验证）
3. 端到端测试（业务验证）
4. 代码覆盖率检查

```yaml
# 示例GitHub Actions配置
- name: Run Tests
  run: |
    python scripts/test.py unit
    python scripts/test.py api -k "not auth"
```
